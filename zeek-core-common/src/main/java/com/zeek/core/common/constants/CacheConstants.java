package com.zeek.core.common.constants;


/**
 * <AUTHOR>
 */
public interface CacheConstants {

    String KEY_PREFIX = "ZEEK" + "::";
    String KEY_LOCK_PREFIX = KEY_PREFIX + "LOCK:";

    /**
     * 事件处理分布式锁
     */
    String CUSTOMER_EVENT_LOCK = KEY_PREFIX + "customer:event:lock:%s:%s";
    String WEB3_EVENT_LISTENER_BLOCK_NUMBER = "zeek_event:listener_block_number";

    String ZEEK_EVENT_PROCESS_TAG = "zeek_event:process_tag:";
    String OSP_PRICE_KEY = KEY_PREFIX + "osp:symbol:price";


    String OWO_RULE = KEY_PREFIX + "owoCount";

    String NFT_BALANCE = CacheConstants.KEY_PREFIX + "nft:balance:cache:";

    String NFT_MINT_LOCK = CacheConstants.KEY_PREFIX + "nft:mint:check:%s";
    static String getNFTMintLockKey(String address) {
        return String.format(NFT_MINT_LOCK, address);
    }

    String NFT_MINT_LOCK_CODE = "nft_mint_lock_code:";
    static String getNFTMintLockCodeKey(String code) {
        return NFT_MINT_LOCK_CODE + code;
    }

    String NFT_MINT_USER = "nft_mint_user:";
    static String getNFTMintUserKey(String customerId) {
        return NFT_MINT_USER + customerId;
    }

    String TOKEN_PRICE = "token_price:";
    static String getTokenPriceKey(String chain, String tokenAddress) {
        return TOKEN_PRICE + chain + ":" + tokenAddress;
    }

    String WALLET_EARLIEST_TRANSACTION = "Wallet_Earliest_Transaction:%s:%s";
    static String getWalletEarliestTransactionKey(String chain, String address) {
        return String.format(WALLET_EARLIEST_TRANSACTION, chain, address);
    }

    String NFT_COLLECTION = "nft_collection";
    String NFT_CONFIG = "nft_config";
    String PROFILE_SIGN = "profile:sign:";

    String BADGE_PROCESS_LOCK = "badge_process_lock:%s";
    static String getBadgeProcessLock(String customerId) {
        return String.format(BADGE_PROCESS_LOCK, customerId);
    }

    String BADGE_CHECK_RESULT = "badge_check_result:%s";
    static String getBadgeCheckResultKey(String customerId) {
        return String.format(BADGE_CHECK_RESULT, customerId);
    }

    String WISH_TRANSFER_LOCK = "wish_transfer_lock:%s";
    static String getWishTransferLock(String wishId) {
        return String.format(BADGE_PROCESS_LOCK, wishId);
    }

    String MINT_MYSTERY_BOX_NFT_TOTAL = "mint_mystery_box_nft_total";
    static String getMintMysteryBoxNftTotalKey() {
        return KEY_PREFIX + MINT_MYSTERY_BOX_NFT_TOTAL;
    }

    /**
     * opensea nft metadata
     * ex: nft:metadata:{address}:{tokenId}
     */
    String NFT_METADATA = "nft:metadata:%s:%s";
    static String getNftMetadata(String address, Integer tokenId) {
        return String.format(NFT_METADATA, address, tokenId);
    }

    String WISH_TRADE_VOLUME = "wish:volume:trade";

    String WISH_METADATA_CACHE = "wish:metadata";

    String QUEST_VERIFY_LOCK = KEY_PREFIX + "quest:verify:lock:%s";

    String PROFILE_CACHE = KEY_PREFIX + "profile:cache:%s:%s";
    String PROFILE_V2_CACHE = KEY_PREFIX + "profile_v2:cache:%s:%s";

    static String getProfileCacheKey(Long chainId, String customerId) {
        return String.format(PROFILE_CACHE, chainId, customerId);
    }

    static String getProfileV2CacheKey(Long chainId, String customerId) {
        return String.format(PROFILE_V2_CACHE, chainId, customerId);
    }


    String GRANT_BADGE_LOCK = KEY_PREFIX + KEY_LOCK_PREFIX + "grant_badge_Lock:%s";
    static String getGrantBadgeLock(String customerId) {
        return String.format(GRANT_BADGE_LOCK, customerId);
    }
    String GRANT_BADGE_IDEMPOTENT_KEY = "grant_badge_idempotent_key:Season_%s:Week_%s:%s";
    static String getGrantBadgeIdempotentKey(Long seasonId, Long weekId, String customerId) {
        return String.format(GRANT_BADGE_IDEMPOTENT_KEY, seasonId, weekId, customerId);
    }

    String OPENSEA_NFT_HOLDER_CRAWLER_LOCK = "opensea_nft_holder_crawler_lock";

    String ANCHOR_ACCESS_TOKEN = "anchor:access_token:%s";
    static String getAnchorAccessTokenKey(String projectId) {
        return String.format(ANCHOR_ACCESS_TOKEN, projectId);
    }

    String SWAP_LOCK = KEY_PREFIX + "swap:%s";
    static String getSwapLockKey(String txHash) {
        return String.format(SWAP_LOCK, txHash);
    }

    /**
     * 预测市场投票结果缓存
     */
    String MARKET_VOTE_RESULT = KEY_PREFIX + "market:vote:result:%s";
    static String getMarketVoteResultKey(String marketId) {
        return String.format(MARKET_VOTE_RESULT, marketId);
    }

    /**
     * 工厂implementationMaster缓存key
     */
    String IMPLEMENTATION_MASTER = KEY_PREFIX + "market:factory:implementation_master";

    /**
     * 市场代币参数缓存
     */
    String MARKET_TOKENS = KEY_PREFIX + "market:tokens:%s";
    static String getMarketTokensKey(String collateralToken) {
        return String.format(MARKET_TOKENS, collateralToken);
    }
}

package com.zeek.core.server.business.prediction.service;

import com.zeek.core.api.*;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.contract.model.event.prediction.*;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.server.business.prediction.model.MarketActionContext;

import java.io.IOException;
import java.math.BigInteger;
import java.util.List;

/**
 * @author: liz<PERSON><PERSON>
 * @date: 2025/5/15 19:03
 */
public interface PredictionService {

    PredictionConfigDTO configs();

    PredictionMarketDTO createMarket(CreatMarketRequest request) throws BaseException;

    boolean handleMarketOnChain(MarketAMMCreatedEventLog lmsrMarketCreationEventLog) throws BaseException;

    boolean handleAddFundingOnChain(MarketAMMFundingAddedEventLog eventLog) throws BaseException;

    boolean handleTradeOnChain(MarketAMMTokenTradeEventLog eventLog) throws BaseException;

    boolean handleCloseMarketOnChain(MarketAMMClosedEventLog event) throws BaseException;

    boolean handleClaimOnChain(MarketAMMClaimedEventLog event) throws BaseException;

    boolean expiredPrediction(MarketActionContext context) throws BaseException;

    CloseMarketResponse closeMarket(CloseMarketRequest request) throws BaseException, IOException, IllegalAccessException;

    boolean settleMarket(PredictionMarketsDO marketId, List<BigInteger> marketResult) throws BaseException;

    /**
     * 缓存 implementationMaster 到 redis
     * @param implementationMaster implementationMaster 地址
     */
    boolean setImplementationMasterToCache(String implementationMaster);

    /**
     * 获取 implementationMaster
     * @return implementationMaster 地址
     */
    String getImplementationMaster();

}

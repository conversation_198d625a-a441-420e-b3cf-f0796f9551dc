package com.zeek.core.server.business.contract.service.processor.prediction;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.FactoryParametersSetEventLog;
import com.zeek.core.server.business.contract.service.EventLogProcessor;
import com.zeek.core.server.business.prediction.service.PredictionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component(EventTypeConstants.MARKET_AMM_FACTORY_PARAMETERS_SET_EVENT)
public class FactoryParametersSetEventProcessor implements EventLogProcessor<FactoryParametersSetEventLog> {

    @Resource
    private PredictionService predictionService;

    @Override
    public FactoryParametersSetEventLog parseEventLog(String message) {
        return JSON.parseObject(message, FactoryParametersSetEventLog.class);
    }

    @Override
    @TracingSpan(business = TracingBusiness.none)
    public boolean process(FactoryParametersSetEventLog eventLog) {
        log.info("FactoryParametersSetEventProcessor process eventLog:{}", JSON.toJSONString(eventLog));

        String collateralToken = eventLog.getCollateralToken();

        // 缓存代币相关字段 (collateralToken, collateralUnit) 到 market:tokens:{collateralToken}
        boolean tokensResult = predictionService.setMarketTokensToCache(collateralToken, eventLog);
        if (!tokensResult) {
            log.error("FactoryParametersSetEventProcessor cache tokens fail, eventLog:{}", JSON.toJSONString(eventLog));
            return false;
        }

        // 缓存费用相关字段 (fee, creatorFee, proposalFee) 到 market:fee:{collateralToken}
        boolean feeResult = predictionService.setMarketFeeToCache(collateralToken, eventLog);
        if (!feeResult) {
            log.error("FactoryParametersSetEventProcessor cache fee fail, eventLog:{}", JSON.toJSONString(eventLog));
            return false;
        }

        log.info("FactoryParametersSetEventProcessor process success, collateralToken: {}", collateralToken);
        return true;
    }
}
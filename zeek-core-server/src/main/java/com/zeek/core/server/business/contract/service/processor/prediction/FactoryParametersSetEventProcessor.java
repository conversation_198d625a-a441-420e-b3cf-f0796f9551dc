package com.zeek.core.server.business.contract.service.processor.prediction;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.FactoryParametersSetEventLog;
import com.zeek.core.server.business.contract.service.EventLogProcessor;
import com.zeek.core.server.business.prediction.service.PredictionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component(EventTypeConstants.MARKET_AMM_FACTORY_PARAMETERS_SET_EVENT)
public class FactoryParametersSetEventProcessor implements EventLogProcessor<FactoryParametersSetEventLog> {

    @Resource
    private PredictionService predictionService;

    @Override
    public FactoryParametersSetEventLog parseEventLog(String message) {
        return JSON.parseObject(message, FactoryParametersSetEventLog.class);
    }

    @Override
    @TracingSpan(business = TracingBusiness.none)
    public boolean process(FactoryParametersSetEventLog eventLog) {
        log.info("FactoryParametersSetEventProcessor process eventLog:{}", JSON.toJSONString(eventLog));

        // 将 eventLog 中的所有字段以 JSON 格式缓存到 redis
        String collateralToken = eventLog.getCollateralToken();
        boolean result = predictionService.setMarketTokensToCache(collateralToken, eventLog);
        if (!result) {
            log.error("FactoryParametersSetEventProcessor process fail, eventLog:{}", JSON.toJSONString(eventLog));
            return false;
        }

        return true;
    }
}
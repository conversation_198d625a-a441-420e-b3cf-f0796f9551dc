package com.zeek.core.server.business.contract.service.processor.prediction;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.FactoryImplementationSetEventLog;
import com.zeek.core.server.business.contract.service.EventLogProcessor;
import com.zeek.core.server.business.prediction.service.PredictionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component(EventTypeConstants.MARKET_AMM_FACTORY_IMPLEMENTATION_SET_EVENT)
public class FactoryImplementationSetEventProcessor implements EventLogProcessor<FactoryImplementationSetEventLog> {

    @Resource
    private PredictionService predictionService;

    @Override
    public FactoryImplementationSetEventLog parseEventLog(String message) {
        return JSON.parseObject(message, FactoryImplementationSetEventLog.class);
    }

    @Override
    @TracingSpan(business = TracingBusiness.none)
    public boolean process(FactoryImplementationSetEventLog eventLog) {
        log.info("FactoryImplementationSetEventProcessor process eventLog:{}", JSON.toJSONString(eventLog));
        // 缓存 implementationMaster 到 redis
        String implementationMaster = eventLog.getImplementationMaster();
        boolean result = predictionService.setImplementationMasterToCache(implementationMaster);
        if (!result) {
            log.error("FactoryImplementationSetEventProcessor process fail, eventLog:{}", JSON.toJSONString(eventLog));
            return false;
        }
        return true;
    }
}